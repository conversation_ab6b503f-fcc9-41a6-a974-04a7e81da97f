-- 全局调试标志
_G.isDebug = false


-- 跳过UI界面标志（用于快速启动，使用默认配置）
_G.skipUI = false

-- 保护措施：面向用户发布时，强制重置调试标志
if not checkIsDebug() then
    _G.isDebug = false
    _G.skipUI = false
    print("面向用户模式：已重置所有调试标志")
end

-- 主流程
if _G.isDebug then

    -- 调试模式下的测试代码
    print("调试模式启动")

    --加载需要的模块
    require("init.init")
    local utils = require("init.utils")
    local char = require("character")
    local common = require("init.common")

    --启动全局提示器
    _G.hudManager = utils.HUDManager:new()

else
    -- 正常模式：启动新的自动化系统
    print("996_agent 启动")

    -- 进行初始化
    require("init.init")
    print("初始化完成，配置已合并")

    -- 启动全局提示器
    local utils = require("init.utils")
    _G.hudManager = utils.HUDManager:new()
    
    -- 加载核心逻辑模块
    local coreLogic = require("coreLogic")
    
    -- 启动主线挂机系统
    print("正在启动核心逻辑系统...")
    _G.hudManager:update({text = "正在启动核心逻辑系统..."})
    sleep(1000)


    -- 开始挂机（增加重新启动机制）
    while true do
        print("启动挂机循环")
        coreLogic.startHanging()
        
        -- 如果主循环意外退出，等待5秒后重新启动
        print("主循环退出，5秒后重新启动...")
        sleep(5000)
        
        -- 重置状态
        coreLogic.resetState()
    end

end

