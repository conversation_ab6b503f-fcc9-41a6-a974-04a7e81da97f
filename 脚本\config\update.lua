local M = {}

-- 将 UI 配置数据合并/更新到业务配置（config.userConfig）
-- 使用方式：
--   local config = require("config.config")
--   require("config.update").apply(config, uiConfig)
function M.apply(config, uiConfig)
    if not config or type(config) ~= "table" then
        print("警告：config 为空或不是表格类型")
        return
    end
    if not uiConfig or type(uiConfig) ~= "table" then
        print("警告：uiConfig 为空或不是表格类型")
        return
    end

    print("开始更新用户配置...")

    -- 辅助函数：安全转换为布尔值
    local function toBool(value)
        if type(value) == "string" then
            return value == "true"
        elseif type(value) == "boolean" then
            return value
        else
            return false
        end
    end

    -- 辅助函数：安全转换为数字
    local function toNumber(value, default)
        local num = tonumber(value)
        return num or (default or 0)
    end

    -- 辅助函数：安全获取字符串值
    local function toString(value, default)
        if type(value) == "string" then
            return value
        else
            return default or ""
        end
    end

    -- 1. 更新定时重启配置
    if uiConfig["定时重启"] ~= nil then
        config.userConfig.定时重启.enable = toBool(uiConfig["定时重启"])
        print("更新定时重启: " .. tostring(config.userConfig.定时重启.enable))
    end

    if uiConfig["定时重启时间"] ~= nil then
        config.userConfig.定时重启.重启时间 = toNumber(uiConfig["定时重启时间"], 30)
        print("更新定时重启时间: " .. tostring(config.userConfig.定时重启.重启时间))
    end

    -- 2. 更新地图配置
    if uiConfig["进入地图"] ~= nil then
        config.userConfig.map_enabled.enable = toBool(uiConfig["进入地图"])
        print("更新进入地图: " .. tostring(config.userConfig.map_enabled.enable))
    end

    -- 地图选择（下图模式对应spinner的索引）
    if uiConfig["下图模式"] ~= nil then
        local mapIndex = toNumber(uiConfig["下图模式"], 0)
        local mapList = { "无尽幽白一层", "离火圣殿二层", "时空幽谷", "遗忘之地" }
        if mapIndex >= 0 and mapIndex < #mapList then
            config.userConfig.map = mapList[mapIndex + 1] -- Lua数组从1开始
            print("更新地图选择: " .. tostring(config.userConfig.map))
        end
    end

    -- 3. 更新喊话设置
    if uiConfig["喊话1"] ~= nil then
        config.userConfig.喊话设置.喊话1.enabled = toBool(uiConfig["喊话1"])
        print("更新喊话1启用: " .. tostring(config.userConfig.喊话设置.喊话1.enabled))
    end

    if uiConfig["喊话1内容"] ~= nil then
        config.userConfig.喊话设置.喊话1.content = toString(uiConfig["喊话1内容"], "")
        print("更新喊话1内容: " .. tostring(config.userConfig.喊话设置.喊话1.content))
    end

    if uiConfig["喊话1间隔"] ~= nil then
        config.userConfig.喊话设置.喊话1.interval = toNumber(uiConfig["喊话1间隔"], 15)
        print("更新喊话1间隔: " .. tostring(config.userConfig.喊话设置.喊话1.interval))
    end

    -- 喊话2设置
    if uiConfig["喊话2"] ~= nil then
        config.userConfig.喊话设置.喊话2.enabled = toBool(uiConfig["喊话2"])
        print("更新喊话2启用: " .. tostring(config.userConfig.喊话设置.喊话2.enabled))
    end

    if uiConfig["喊话2内容"] ~= nil then
        config.userConfig.喊话设置.喊话2.content = toString(uiConfig["喊话2内容"], "")
        print("更新喊话2内容: " .. tostring(config.userConfig.喊话设置.喊话2.content))
    end

    if uiConfig["喊话2间隔"] ~= nil then
        config.userConfig.喊话设置.喊话2.interval = toNumber(uiConfig["喊话2间隔"], 20)
        print("更新喊话2间隔: " .. tostring(config.userConfig.喊话设置.喊话2.interval))
    end

    -- 4. 更新武林争霸配置
    if uiConfig["武林争霸"] ~= nil then
        config.userConfig.武林争霸.enable = toBool(uiConfig["武林争霸"])
        print("更新武林争霸: " .. tostring(config.userConfig.武林争霸.enable))
    end

    -- 5. 更新世界Boss配置
    if uiConfig["世界Boss"] ~= nil then
        config.userConfig.世界Boss.enable = toBool(uiConfig["世界Boss"])
        print("更新世界Boss: " .. tostring(config.userConfig.世界Boss.enable))
    end

    -- 6. 更新禁地配置
    if uiConfig["禁地"] ~= nil then
        config.userConfig.禁地.enable = toBool(uiConfig["禁地"])
        print("更新禁地: " .. tostring(config.userConfig.禁地.enable))
    end

    -- 禁地层数配置
    do
        local 禁地层数 = {}
        for i = 1, 5 do
            local key = "禁地" .. i .. "层"
            if uiConfig[key] ~= nil and toBool(uiConfig[key]) then
                table.insert(禁地层数, i .. "层")
                print("启用禁地" .. i .. "层")
            end
        end
        if #禁地层数 > 0 then
            config.userConfig.禁地.层数 = 禁地层数
            print("更新禁地层数: " .. table.concat(禁地层数, ", "))
        end
    end

    -- 7. 更新个人首领配置
    if uiConfig["个人首领"] ~= nil then
        config.userConfig.个人首领.enable = toBool(uiConfig["个人首领"])
        print("更新个人首领: " .. tostring(config.userConfig.个人首领.enable))
    end

    -- 个人首领分配次数
    do
        local 分配次数 = {}
        if uiConfig["个人首领_元素"] ~= nil then
            分配次数.元素 = toNumber(uiConfig["个人首领_元素"], 1)
            print("更新个人首领元素次数: " .. tostring(分配次数.元素))
        end
        if uiConfig["个人首领_装备"] ~= nil then
            分配次数.装备 = toNumber(uiConfig["个人首领_装备"], 1)
            print("更新个人首领装备次数: " .. tostring(分配次数.装备))
        end
        if uiConfig["个人首领_龙脉"] ~= nil then
            分配次数.龙脉 = toNumber(uiConfig["个人首领_龙脉"], 1)
            print("更新个人首领龙脉次数: " .. tostring(分配次数.龙脉))
        end
        if uiConfig["个人首领_神器"] ~= nil then
            分配次数.神器 = toNumber(uiConfig["个人首领_神器"], 5)
            print("更新个人首领神器次数: " .. tostring(分配次数.神器))
        end

        if next(分配次数) then
            for key, value in pairs(分配次数) do
                config.userConfig.个人首领.分配次数[key] = value
            end
        end
    end

    -- 8. 更新入道天途配置
    if uiConfig["入道天途"] ~= nil then
        config.userConfig.入道天途.enable = toBool(uiConfig["入道天途"])
        print("更新入道天途: " .. tostring(config.userConfig.入道天途.enable))
    end

    -- 9. 更新升级宝箱配置（对应UI中的"领取宝箱"）
    if uiConfig["领取宝箱"] ~= nil then
        config.userConfig.升级宝箱.enable = toBool(uiConfig["领取宝箱"])
        print("更新升级宝箱: " .. tostring(config.userConfig.升级宝箱.enable))
    end

    -- 10. 更新散人福利配置
    if uiConfig["散人福利"] ~= nil then
        config.userConfig.散人福利.enable = toBool(uiConfig["散人福利"])
        print("更新散人福利: " .. tostring(config.userConfig.散人福利.enable))
    end

    -- 11. 更新星空秘境配置
    if uiConfig["星空秘境"] ~= nil then
        config.userConfig.星空秘境 = { enable = toBool(uiConfig["星空秘境"]) }
        print("更新星空秘境: " .. tostring(config.userConfig.星空秘境.enable))
    end

    -- 12. 更新千里传音配置
    if uiConfig["千里传音（本服）"] ~= nil then
        config.userConfig.千里传音.enable = toBool(uiConfig["千里传音（本服）"])
        print("更新千里传音: " .. tostring(config.userConfig.千里传音.enable))
    end

    if uiConfig["千里传音内容"] ~= nil then
        config.userConfig.千里传音.传音内容 = toString(uiConfig["千里传音内容"], "")
        print("更新千里传音内容: " .. tostring(config.userConfig.千里传音.传音内容))
    end

    -- 13. 更新活跃地图配置
    if uiConfig["活跃地图"] ~= nil then
        config.userConfig.活跃地图.enable = toBool(uiConfig["活跃地图"])
        print("更新活跃地图: " .. tostring(config.userConfig.活跃地图.enable))
    end

    print("用户配置更新完成")
end

return M