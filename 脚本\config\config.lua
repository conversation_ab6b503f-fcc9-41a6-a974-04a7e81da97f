local config = {}

-- 迁移：从独立文件加载用户配置
config.userConfig = require("config.userConfig")

config.uiConfig = {
    打宝 = { 936, 15, 1013, 59, "f3794b-202020", "1|1|f3794b-202020|3|2|f3794b-202020|4|3|f3794b-202020|6|3|f5581d-202020|7|4|e04c36-202020|8|4|853018-202020|9|4|de9b4a-202020|10|5|f9ec5a-202020|11|4|fbf57e-202020|12|5|de9b4a-202020|13|5|f3794b-202020|13|3|f3794b-202020|12|2|f3794b-202020|12|0|fdf89f-202020", 0, 0.85 },

    激战Boss = { 847, 8, 935, 88, "8b6c3c-202020", "3|0|83724b-202020|5|0|b8a169-202020|7|0|7e683b-202020|10|0|573d1e-202020|11|0|7d582e-202020|10|3|d8a75c-202020|9|5|f4c36e-202020|9|8|9e7636-202020|7|10|e0ba45-202020|7|11|f2e192-202020|7|12|fbfbf5-202020|8|14|c99c4a-202020|11|14|f1d255-202020|13|14|fdf9db-202020|17|13|ba9e43-202020|18|13|b58e4c-202020", 0, 0.85 },

    散人福利 = { 936, 15, 1013, 59, "fbf57e-202020", "1|1|f4e495-202020|4|2|e5cf74-202020|6|3|ceb563-202020|8|5|ab843f-202020|8|7|99602e-202020|8|8|a0511f-202020|8|9|9b3719-202020|6|9|8d4530-202020|12|9|853018-202020|14|9|853018-202020|17|9|853018-202020|17|6|e4b975-202020", 0, 0.85 },

    活动 = { 783, 17, 827, 43, "b11e0e-202020", "4|0|9b3719-202020|7|-2|853018-202020|10|-3|e04c36-202020|12|-2|e04c36-202020|12|1|853018-202020|12|3|501f0e-202020|15|3|433119-202020|17|3|e3d28e-202020|22|3|755129-202020|25|4|9b3719-202020", 0, 0.85 },

    商城 = { 600, 4, 677, 50, "e3bc46-202020", "0|1|dfa22b-202020|0|2|a0511f-202020|0|3|713116-202020|0|4|f1d255-202020|0|5|fbf57e-202020|1|6|f1d255-202020|1|8|905b17-202020|1|8|905b17-202020|2|10|563d1e-202020|2|11|713116-202020|-31|-21|21262b-202020", 0, 0.85 },

    活动页面_变化 = { 1034, 29 },

    目标_怪物 = { 753, 389, 802, 427, "efd486-101010", "0|1|ecc771-101010|-1|3|ecc771-101010|0|5|e8b663-101010|0|6|e8b663-101010|1|8|dbab52-101010|4|8|dbab52-101010|6|8|dbab52-101010|9|8|dbab52-101010|9|9|dbab52-101010|10|11|d49b3d-101010|10|12|d49b3d-101010|13|9|dbab52-101010|17|8|e8b663-101010|20|8|dbab52-101010|21|8|dbab52-101010|22|6|e8b663-101010|21|2|ecc771-101010", 0, 0.8 },

    目标_变化 = { 1083, 305 },

    目标_未选中怪物 = { 757, 390, 798, 427, "ddd09b-202020", "-1|1|dcd09b-202020|-3|3|d6c686-202020|-2|7|cbb77b-202020|-1|7|cbb77b-202020|1|8|b9a771-202020|4|7|cbb77b-202020|6|6|7f6e40-202020|12|7|cbb77b-202020|15|8|a28d58-202020|18|8|b9a771-202020|19|6|cbb77b-202020|19|3|d6c686-202020|17|1|b9a771-202020", 0, 0.8 },

    自动挂机中 = { 672, 446, 707, 478, "f0f4f4-202020", "1|2|d6e8f2-202020|0|4|ade3ee-202020|0|6|bdddf0-202020|1|6|b3deee-202020|1|8|9fdee9-202020|1|10|8ae7ea-202020|2|10|90e6eb-202020|5|9|89e7ea-202020|7|7|b6deef-202020|-7|9|89e7ea-202020", 0, 0.8 },

    挂机按钮 = { 1169, 297 },

    红点 = { "fbf8e3-101010", "0|1|a77375-101010|0|3|612c28-101010|0|3|612c28-101010|0|4|600f09-101010|0|5|600f09-101010|0|6|600f09-101010|0|7|701b0d-101010|1|8|86170c-101010|0|9|86170c-101010|0|10|86170c-101010|1|11|a4190e-101010|2|12|de4318-101010|4|12|f1744c-101010|5|12|f69447-101010|7|12|f6e59c-101010", 0, 0.8 },

    激战Boss子页面 = {
        个人首领 = { 281, 415 },

        世界首领 = { 443, 250 },

        禁地 = { 564, 492 },

        入道天途 = { 723, 276 },

        武林争霸 = { 840, 475 }
    },

    真充红包 = {
        检测区域 = { 54, 74, 294, 177 },
        领取奖励 = { 896, 512, 1124, 672 },
    },

    打宝地图 = {
        无尽幽白一层 = {
            {
                type = "swipe",
                参数 = { 235, 246, 254, 585, 500 },
                重复次数 = 2,
                间隔时间 = 1000
            },
            {
                type = "tap",
                参数 = { 252, 237 },
                间隔时间 = 2000
            },
            {
                type = "tap",
                参数 = { 1025, 590 },
                间隔时间 = 1500
            }
        },
        时空幽谷 = {
            {
                type = "ocr_tap",
                参数 = {
                    ocr_area = { 173, 136, 357, 655 },
                    search_text = "第12大陆",
                    间隔时间 = 1500
                }
            },
            {
                type = "tap",
                参数 = { 1024, 410 },
                间隔时间 = 1500
            }
        },
        离火圣殿二层 = {
            {
                type = "swipe",
                参数 = { 235, 246, 254, 585, 500 },
                重复次数 = 2,
                间隔时间 = 1000
            },
            {
                type = "tap",
                参数 = { 252, 237 },
                间隔时间 = 2000
            },
            {
                type = "swipe",
                参数 = { 738, 618, 719, 346, 800 },
                重复次数 = 1,
                间隔时间 = 1000
            },
            {
                type = "tap",
                参数 = { 1032, 614 },
                间隔时间 = 1500
            }
        },
        遗忘之地 = {
            {
                type = "swipe",
                参数 = { 235, 246, 254, 585, 500 },
                重复次数 = 2,
                间隔时间 = 1000
            },
            {
                type = "tap",
                参数 = { 249, 446 },
                间隔时间 = 2000
            },
            {
                type = "tap",
                参数 = { 1023, 417 },
                间隔时间 = 1500
            }
        },
        活跃地图 = {
            {
                type = "tap",
                参数 = { 1029, 337 },
                间隔时间 = 2000
            },
        }
    },

    进入游戏标识 = { 405, 651, 445, 686, "181208-101010", "1|1|330f07-101010|2|2|5b100b-101010|3|3|72180d-101010|3|4|bb3510-101010|3|4|bb3510-101010|3|5|bb3510-101010|3|6|bb3510-101010|3|8|bb3510-101010|1|8|65240c-101010|1|9|95281d-101010|0|11|5b100b-101010|7|9|72180d-101010|7|11|bfb9a9-101010|7|12|fbf8d1-101010", 0, 0.8 },

    回城石 = { 457, 545, 819, 610, "11283c-202020", "-2|2|6887ac-202020|-4|4|204e7f-202020|-5|6|30619a-202020|-4|8|375e89-202020|0|8|386ea7-202020|4|8|164f84-202020|6|10|1760a3-202020|8|14|577290-202020", 0, 0.8 },

    随机石 = {425,530,835,625,"916c07-202020","-1|2|8f6d10-202020|-1|4|a68211-202020|-1|6|a29851-202020|-1|7|9d955a-202020|-1|9|a5a27c-202020|-1|11|bcc2c1-202020|-1|13|a7afb0-202020|-1|16|62532e-202020|1|18|61460b-202020|2|20|604818-202020|-43|-91|3e372e-202020",0,0.85},

    喊话 = {
        打开喊话界面 = { 615, 668 },

        世界界面 = { 17, 310, 60, 375, "f6f6e9-101010", "0|1|f3f3e6-101010|0|3|f2f2e5-101010|0|4|f2f2e5-101010|-1|3|98968f-101010|-1|3|98968f-101010|-1|4|98968f-101010|-2|4|2e2b23-101010|-2|4|2e2b23-101010|-3|5|332f21-101010", 0, 0.8 },

        关闭喊话页面 = { 451, 97 },

        发送 = { 355, 330, 421, 366, "725327-101010", "1|6|725428-101010|3|9|684e29-101010|5|7|5e4e36-101010|7|7|eeede1-101010|13|6|87857f-101010|14|9|928d82-101010|20|10|533f22-101010|21|10|8a8882-101010|20|13|c8c7bd-101010|-15|-11|3a3423-101010", 0, 0.9 },

        喊话输入框 = { 157, 684 },


    },

    挂机设置 = {
        目标_第一个怪物 = { 836, 316 },
    },

    背包 = { 1247, 223 },

    散人福利_选项页面 = { 156, 128, 343, 646 },

    散人福利_领取页面 = { 346, 132, 1113, 661 },

    关闭页面 = { 1068, 75, 1123, 134, "5b6150-202020", "1|1|6a725d-202020|1|3|a8aea4-202020|1|4|969e8f-202020|1|6|27221b-202020|2|7|7c1c0d-202020|2|8|9b1e0a-202020|3|10|9b1e0a-202020|3|13|c1430e-202020|3|15|9c1f0b-202020|5|16|9d200c-202020|6|17|9d200c-202020|8|19|7d1d0e-202020|4|7|9b1e0a-202020|5|6|9b1e0a-202020|7|4|7d1d0d-202020", 0, 0.8 },

    入道天途 = {
        扫荡上层 = { 431, 604 },
        红点 = { 732, 176, 794, 252 },
    },


    禁地 = {
        一层 = { 262, 186 },
        二层 = { 461, 165 },
        三层 = { 647, 178 },
        四层 = { 872, 178 },
        五层 = { 1079, 178 },

        红点 = { 593, 385, 632, 447 },

        一键扫荡 = { 968, 605 },

    },

    世界首领 = {
        攻打完毕时间 = 15,
        领取奖励 = { 586, 551, 650, 605, "d2d1d1-202020", "0|1|dcdcdc-202020|0|2|cccccc-202020|-1|2|d2d1d1-202020|-2|3|d2d1d1-202020|-3|4|d1d1d1-202020|-3|5|cac9c9-202020|-4|5|cbcaca-202020|-4|6|d3d3d3-202020|-5|6|c2c1c1-202020|-5|7|bfbebe-202020|0|2|cccccc-202020|1|2|cac9c9-202020|1|3|bab9b9-202020|2|3|cbcaca-202020|2|4|c0bfbf-202020|3|4|c9c8c8-202020|3|5|cbcaca-202020|4|5|bfbebe-202020|4|6|b9b8b8-202020|-2|-2|99622e-202020|-2|-1|754d2c-202020|-3|0|905a2c-202020|-4|1|a86431-202020|-5|3|93522b-202020|-5|3|93522b-202020", 0, 0.85 },

        红点 = { 481,168,557,227 },
    },

    个人首领 = {
        元素 = { 279, 614 },
        装备 = { 519, 602 },
        龙脉 = { 753, 609 },
        神器 = { 981, 606 },

        红点 = { 340, 338, 369, 370 },

        打Boss标识 = { 734, 2, 773, 39, "e9ba66-202020", "1|2|e1b05a-202020|4|4|d9a74d-202020|5|4|be964a-202020|7|6|c29943-202020|9|9|b48435-202020|11|11|a5792f-202020|13|13|9a6e2c-202020|13|0|e9ba66-202020|8|4|d9a74d-202020|6|7|b69042-202020|2|12|9f7226-202020|-1|14|986923-202020", 0, 0.8 },

        完毕阈值 = 10
    },

    武林争霸 = {
        开战 = { 959, 493, 1052, 583, "fbf3af-202020", "3|1|fef9a4-202020|5|1|fdf9ab-202020|6|5|edd97b-202020|10|9|fcec74-202020|10|13|fce172-202020|10|18|ecc660-202020|10|23|f1a850-202020|10|27|f1954a-202020|10|32|e77e40-202020|18|35|fdf176-202020|20|39|f2d16c-202020|20|44|f1ce5b-202020|20|47|bc6a2d-202020", 0, 0.8 },
        开战超时 = 30,
        关闭按钮 = { 1128, 103 },
        取消 = { 495, 431, 555, 461, "fff4bb-202020", "1|2|927d51-202020|1|7|927d51-202020|0|9|fff4bb-202020|0|12|fff4bb-202020|0|13|fff4bb-202020|6|1|fff4bb-202020|7|1|fff4bb-202020|10|1|fff4bb-202020|12|2|f2e5ae-202020|11|5|fff4bb-202020|10|9|fff4bb-202020|9|12|f3e6af-202020|20|13|fff4bb-202020|20|16|634a23-202020", 0, 0.8 },

        红点 = { 863,368,927,445 },
    },

    领取宝箱 = {
        tap集 = {
            { 1246, 223 },
            { 1089, 663 },
            { 1203, 306 },
            { 932, 301 },
            { 635, 438 }
        },
        升级宝箱 = { 1033, 639 },
        背包关闭 = { 571, 98 },
        升级次数 = 40,
    },

    商城购买 = {
        随机石 = { 537, 263 },
        回城石 = { 784, 265 },
        确认 = { 739, 476 },
        增加 = { 745, 347 },
        随机石购买次数 = 2,
        回城石购买次数 = 2
    },

    弹窗处理 = {
        点击前往关闭 = { 1046, 195 }
    },

    星空秘境 = {
        活动名字区域 = { 328, 133, 595, 640 },
        前往偏移 = { 200, 10 },
        前往地图 = { 756, 592 },
        已死亡 = { 599, 142, 802, 246, "fc5857-202020", "0|7|e73e41-202020|-1|10|e73334-202020|-1|15|d42122-202020|5|15|e73334-202020|4|22|cf1b1c-202020|17|19|dd3032-202020|18|23|c91616-202020|26|18|dd3032-202020|30|15|e73334-202020|57|19|d42122-202020|-11|-39|1e1811-202020|75|25|cf1b1c-202020|75|39|c21112-202020", 0, 0.8 },
        领取奖励 = { 481, 519, 829, 633 },
        确定 = { 644, 374, 861, 503 },
        退出 = { 322, 207 }
    },

    加载游戏 = {
        离线收益界面 = { 1028, 14, 1049, 44, "e0d6a4-101010", "1|1|f2e7ae-101010|2|2|f2e7ae-101010|4|3|f2e7ae-101010|5|5|fdfadc-101010|5|5|fdfadc-101010|3|7|776847-101010|2|8|8c7849-101010|0|11|aa935f-101010|2|16|dac787-101010|4|15|b79a5e-101010|6|12|b9a26a-101010|11|9|aa935f-101010|13|6|e0d5a4-101010|14|5|f2efd3-101010|11|3|eadecb-101010|9|1|ede1ab-101010|5|-3|e0d6a4-101010|1|-6|c5b075-101010", 0, 0.8 },

        启动游戏_界面 = { 541, 496, 733, 606, "c7ba6b-202020", "5|-1|beaf61-202020|7|-1|b6a65f-202020|7|4|e1d991-202020|8|8|f0ebb1-202020|8|12|fefcbc-202020|8|16|ede8ab-202020|8|20|cac78a-202020|-1|13|ede8ab-202020|-1|17|e5e0a5-202020|42|10|f2edba-202020|46|9|f8f5bd-202020|49|9|f2edba-202020|47|15|e2dea5-202020|49|19|c1b979-202020|44|20|615739-202020|40|16|d5d39e-202020", 0, 0.9 },

        启动游戏_角色选择 = { 589, 62, 758, 134, "f1e1ac-101010", "2|0|f5e8b5-101010|3|2|665043-101010|2|6|e1ba7a-101010|2|8|e7c88c-101010|3|9|e7c88c-101010|2|13|d7c597-101010|2|15|bfa87d-101010|26|10|e1ba7a-101010|26|12|d3b078-101010|32|16|e6c68a-101010|35|15|e2bc7d-101010|38|15|e5c487-101010|39|19|f9f2c1-101010|-41|-27|140a00-101010", 0, 0.9 },

        公告检测界面 = { 661, 78, 739, 121, "e2bb68-101010", "4|0|e2bb68-101010|7|-1|2f2814-101010|12|0|e2bb68-101010|9|5|e5cd83-101010|9|10|f8eba5-101010|9|13|fcf5b3-101010|1|13|eee7a8-101010|-7|13|fcf5b3-101010|0|16|62400b-101010|14|13|6d440c-101010|14|7|57380b-101010|17|2|4a320f-101010", 0, 0.9 },

        弹窗 = { 1028, 14, 1049, 44, "e0d6a4-101010", "1|1|f2e7ae-101010|2|2|f2e7ae-101010|4|3|f2e7ae-101010|5|5|fdfadc-101010|5|5|fdfadc-101010|3|7|776847-101010|2|8|8c7849-101010|0|11|aa935f-101010|2|16|dac787-101010|4|15|b79a5e-101010|6|12|b9a26a-101010|11|9|aa935f-101010|13|6|e0d5a4-101010|14|5|f2efd3-101010|11|3|eadecb-101010|9|1|ede1ab-101010|5|-3|e0d6a4-101010|1|-6|c5b075-101010", 0, 0.9 },

        关闭 = { { 818, 169, 849, 197, "fbf2b1-101010", "1|0|fbf3aa-101010|1|1|faf09b-101010|2|1|fbf2a4-101010|3|3|faf098-101010|4|3|faf098-101010|5|4|fcea84-101010|6|5|fcea82-101010|7|6|fde675-101010|8|7|fde675-101010|9|8|fde571-101010|9|9|fde571-101010|8|11|fde571-101010|8|13|fbde59-101010|6|13|fbe060-101010|6|14|fadc54-101010|5|15|fadb61-101010|4|18|ac7838-101010|2|18|f0c748-101010|-3|-4|523925-101010", 0, 0.9 } },

        关闭公告 = { 1093, 95 },

        离线收益关闭 = { 628, 539 },

        等待完全加载时间 = 20, --秒

        游戏加载超时时间 = 200, --秒
    },

    千里传音 = {
        传音 = { 948, 673 },
        本服发送 = { 884, 648 },
        输入框 = { 505, 642 },
        关闭传音 = { 1112, 103 },
        重复执行次数 = 2

    },
}

-- 从devConfig.lua加载开发设置
config.devConfig = require("config.devConfig")

-- UI配置合并逻辑迁移至独立模块，保持向后兼容的接口
function config.updateFromUIConfig(uiConfig)
    local updater = require("config.update")
    return updater.apply(config, uiConfig)
end

return config
