# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于Lua的Android游戏自动化脚本，专门为《五霸争雄》手游设计的挂机辅助工具。

## 核心架构

### 主要模块
- `五霸争雄.lua` - 主入口文件，控制调试模式和启动流程
- `init/` - 初始化模块目录
  - `init.lua` - 系统初始化（类加载、配置、OCR模型等）
  - `ui.lua` - 用户界面配置
  - `utils.lua` - 工具类和定时器
  - `common.lua` - 通用功能函数
  - `apiClient.lua` - API客户端
- `coreLogic.lua` - 核心挂机逻辑和任务调度
- `character.lua` - 游戏角色操作和功能
- `config/` - 配置文件目录
  - `config.lua` - 主配置文件
  - `devConfig.lua` - 开发配置
  - `userConfig.lua` - 用户配置
  - `update.lua` - 配置更新逻辑

### 关键设计模式
1. **模块化架构**: 功能按模块分离，通过require加载
2. **配置驱动**: 所有功能通过配置文件控制开关和参数
3. **状态管理**: coreLogic模块管理挂机状态和任务执行
4. **定时器系统**: 基于utils.Timer实现的任务调度
5. **错误恢复**: 使用pcall包装关键操作，确保异常不导致脚本崩溃

## 开发环境

### 运行环境
- Android设备（通过WSL2访问）
- Lua脚本引擎（LuaEngine）
- PaddleOCR ONNX模型支持

### 调试模式
- 设置 `_G.isDebug = true` 开启调试模式
- 设置 `_G.skipUI = true` 跳过UI界面使用默认配置
- 调试模式下会跳过卡密验证和热更检查

### 常用调试方法
```lua
-- 检查调试模式
if _G.isDebug then
    print("调试信息")
end

-- 使用工具类记录日志
utils.sleepLog(1000, "等待操作完成")

-- 更新HUD显示
_G.hudManager:update({text = "状态信息"})
```

## 配置系统

### 配置层次结构
1. `config.devConfig` - 开发配置（版本、包名等）
2. `config.userConfig` - 用户功能配置
3. `config.uiConfig` - 界面元素坐标和识别参数

### 主要配置项
- 地图设置：打宝地图选择和活跃地图时间段
- 任务开关：各种自动任务的启用状态
- 定时设置：重启间隔、喊话频率等
- 坐标配置：UI元素识别的坐标和颜色特征值

## 核心功能模块

### 1. 自动挂机系统（coreLogic.lua）
- 地图进入和自动战斗
- 定时重启机制
- 任务调度和状态管理
- 异常检测和恢复

### 2. 任务系统
**周期性任务**:
- 禁地（6小时间隔）
- 世界首领（15分钟间隔）

**每日定时任务**:
- 22:10 领取宝箱和散人福利
- 20:02 星空秘境  
- 03:00, 15:00 武林争霸
- 19:40, 19:58, 20:29 千里传音
- 00:02 活跃地图进图

### 3. 游戏操作封装（character.lua）
- 进入打宝地图
- 攻击怪物和自动战斗
- 各种游戏功能（禁地、首领、商城购买等）
- 喊话和千里传音

## 文件结构说明

```
五霸争雄.lua          # 主入口
脚本/
├── init/             # 初始化模块
├── config/           # 配置文件
├── coreLogic.lua     # 核心逻辑
├── character.lua     # 角色操作
界面/
└── 五霸争雄.ui       # UI界面文件
资源/
└── 五霸争雄.rc       # 资源文件
```

## 关键注意事项

1. **坐标系统**: 所有UI坐标基于特定分辨率，修改时需要对应调整
2. **OCR依赖**: 部分功能依赖PaddleOCR文字识别，需要模型正确加载
3. **异常处理**: 关键操作都使用pcall包装，避免脚本意外退出
4. **状态同步**: 修改state状态时需要考虑并发和一致性
5. **配置热更**: 支持配置在线更新，注意版本兼容性

## 开发建议

- 新增功能时优先在config中添加开关配置
- 重要操作添加日志输出便于调试
- 使用existing patterns保持代码风格一致
- 测试时建议先开启调试模式验证功能
- 坐标配置建议使用相对位置和容错范围