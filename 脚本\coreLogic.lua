local coreLogic = {}

local character = require("character")
local config = require("config.config")
local utils = require("init.utils")
local common = require("init.common")

-- 状态管理
local state = {
    isHanging = false,  -- 是否在挂机
    isInMap = false,    -- 是否已进图
    isExecutingTask = false,  -- 是否正在执行定时任务
    currentTask = "",   -- 当前执行的任务名称
    worldBossFailCount = 0,  -- 世界首领失败次数
    lastWorldBossDate = "",  -- 上次世界首领日期
}

-- 定时器管理
local timers = {
    restart = nil,      -- 重启定时器
    shoutTimers = {},   -- 喊话定时器组
    forbiddenLand = nil,  -- 禁地定时器
    worldBoss = nil,    -- 世界首领定时器
    dailyTasks = {},    -- 每日任务定时器
    startupTasks = nil, -- 启动任务标记
    frontAppCheckTimer = nil, -- 前台应用检测定时器
    disconnectCheckTimer = nil, -- 断开检测定时器
}

-- 任务执行记录
local taskRecord = {
    startupTasksDone = false,  -- 启动任务是否已完成
    lastStartupDate = "",       -- 上次执行启动任务的日期
}

-- 获取当前时间字符串
local function getCurrentTime()
    return os.date("%Y-%m-%d %H:%M:%S")
end

-- 获取当前日期字符串
local function getCurrentDate()
    return os.date("%Y-%m-%d")
end

-- 获取当前小时和分钟
local function getCurrentHourMinute()
    local t = os.date("*t")
    return t.hour, t.min
end

-- 判断当前是否在活跃地图时间段（00:02-02:02）
local function isActiveMapTime()
    local hour, minute = getCurrentHourMinute()
    local currentMinutes = hour * 60 + minute  -- 转换为总分钟数
    
    -- 00:02 = 2分钟，02:02 = 122分钟
    return (currentMinutes >= 2 and currentMinutes <= 122)
end

-- 更新HUD显示
local function updateHUD(text)
    if _G.hudManager then
        _G.hudManager:update({text = text})
    end
    print("状态更新: " .. text)
end

-- 倒计时显示
local function showCountdown(action, seconds)
    for i = seconds, 1, -1 do
        updateHUD(string.format("%d秒后%s", i, action))
        utils.sleepLog(1000, "倒计时")
    end
end

-- 执行喊话逻辑
local function executeShout(shoutConfig, index)
    if not shoutConfig.enabled then
        return
    end
    
    showCountdown("执行喊话" .. index, 3)
    updateHUD("正在执行喊话" .. index)
    character:执行喊话(shoutConfig.content)
    print(string.format("喊话%d完成: %s", index, shoutConfig.content))
end

-- 执行攻击逻辑
local function executeAttack()
    character:开启目标选择怪物界面()
    utils.sleepLog(10, "等待界面")
    character:攻击第一个目标怪物()
    print("攻击目标怪物完成")
end

-- 进入地图
local function enterMap()
    -- 进图前自动购买随机石和回城石
    print("进图前检查并购买随机石和回城石")
    local buySuccess = character:自动购买随机石和回城石()
    if buySuccess then
        print("自动购买检查完成")
    else
        print("自动购买失败，但继续进图")
    end
    
    local mapConfig = config.userConfig
    if not mapConfig.map_enabled or not mapConfig.map_enabled.enable then
        updateHUD("地图功能未启用，跳过进图")
        return false
    end
    
    local mapName = mapConfig.map
    local isActiveTime = isActiveMapTime()
    
    -- 如果在活跃地图时间段且活跃地图功能开启，使用活跃地图
    if isActiveTime and mapConfig.活跃地图 and mapConfig.活跃地图.enable then
        mapName = "活跃地图"
        updateHUD("活跃地图时间段，正在进入活跃地图: " .. mapName)
        print(string.format("当前时间在活跃地图时间段(00:02-02:02)，进入活跃地图: %s", mapName))
    else
        if not mapName or mapName == "" then
            updateHUD("未设置地图名称，跳过进图")
            return false
        end
        updateHUD("正在进入地图: " .. mapName)
    end
    
    character:进入打宝地图(mapName)
    state.isInMap = true
    updateHUD("已进入地图: " .. mapName)
    return true
end

-- 初始化喊话定时器
local function initShoutTimers()
    local shoutSettings = config.userConfig.喊话设置
    if not shoutSettings then
        return
    end
    
    for i = 1, 10 do
        local key = "喊话" .. i
        local shoutConfig = shoutSettings[key]
        if shoutConfig and shoutConfig.enabled then
            timers.shoutTimers[i] = utils.Timer:new(shoutConfig.interval, "s")
            print(string.format("初始化喊话%d定时器，间隔: %d秒", i, shoutConfig.interval))
        end
    end
end

-- 检查并执行喊话
local function checkAndExecuteShouts()
    local shoutSettings = config.userConfig.喊话设置
    if not shoutSettings then
        return nil
    end
    
    local minTime = 999999
    local nextShout = nil
    
    for i = 1, 10 do
        local key = "喊话" .. i
        local shoutConfig = shoutSettings[key]
        local timer = timers.shoutTimers[i]
        
        if shoutConfig and shoutConfig.enabled and timer then
            if timer:isFinished() then
                executeShout(shoutConfig, i)
                timer:reset()
            else
                local remaining = timer:remaining("s")
                if remaining < minTime then
                    minTime = remaining
                    nextShout = i
                end
            end
        end
    end
    
    if nextShout then
        return string.format("喊话%d:%ds", nextShout, math.ceil(minTime))
    end
    return nil
end

-- 执行定时重启
local function executeRestart()
    updateHUD("正在执行定时重启...")
    print("执行定时重启")
    
    -- 关闭应用
    local packageName = config.devConfig.应用包名
    utils.close_app(packageName)
    updateHUD("已关闭游戏，等待6秒后重新启动")
    utils.sleepLog(6000, "等待游戏关闭")
    
    -- 启动应用
    updateHUD("正在启动游戏...")
    utils.start_app(packageName)
    utils.sleepLog(3000, "等待游戏启动")
    
    -- 重新加载游戏
    local result = character:加载游戏()
    
    if result then
        updateHUD("游戏加载完成")
    else
        updateHUD("游戏加载超时")
    end
    
    -- 重置状态
    state.isInMap = false
    updateHUD("重启完成，需要重新进图")
    
    -- 重新进图
    enterMap()
end

-- 检查定时重启
local function checkRestart()
    -- 仅在挂机状态下检查重启
    if not state.isHanging or state.isExecutingTask then
        return nil
    end
    
    local restartConfig = config.userConfig.定时重启
    if not restartConfig or not restartConfig.enable then
        return nil
    end
    
    if not timers.restart then
        timers.restart = utils.Timer:new(restartConfig.重启时间, "min")
        print(string.format("初始化重启定时器，间隔: %d分钟", restartConfig.重启时间))
    end
    
    if timers.restart:isFinished() then
        executeRestart()
        timers.restart:reset()
        return nil
    else
        local remaining = timers.restart:remaining("min")
        return string.format("重启:%dm", math.ceil(remaining))
    end
end

-- 执行禁地任务
local function executeJinDi()
    state.isExecutingTask = true
    state.currentTask = "禁地"
    updateHUD("正在执行禁地任务...")
    
    local jindiConfig = config.userConfig.禁地
    if jindiConfig and jindiConfig.enable then
        -- 使用pcall包装禁地函数，确保异常不会导致脚本崩溃
        local success, result = pcall(function()
            return character:禁地(jindiConfig.层数)
        end)
        
        if success then
            print("禁地任务完成")
        else
            print("禁地任务执行出错: " .. tostring(result))
        end
    end
    
    state.isExecutingTask = false
    state.currentTask = ""
    state.isInMap = false  -- 执行任务后需要重新进图
end

-- 执行世界首领任务
local function executeWorldBoss()
    -- 检查日期，如果是新的一天，重置计数
    local currentDate = getCurrentDate()
    if state.lastWorldBossDate ~= currentDate then
        state.worldBossFailCount = 0
        state.lastWorldBossDate = currentDate
    end
    
    -- 如果今天已经失败两次，跳过
    if state.worldBossFailCount >= 2 then
        print("今日世界首领已失败2次，跳过执行")
        return
    end
    
    state.isExecutingTask = true
    state.currentTask = "世界首领"
    updateHUD("正在执行世界首领任务...")
    
    -- 使用pcall包装世界首领函数，确保异常不会导致脚本崩溃
    local success, result, reason = pcall(function()
        return character:世界首领()
    end)
    
    if not success then
        print("世界首领任务执行出错: " .. tostring(result))
        -- 执行出错时不增加失败计数，可能是临时性问题
        print("执行出错，稍后重试")
    elseif result == false then
        if reason == "no_attempts" then
            -- 只有无次数时才增加失败计数
            state.worldBossFailCount = state.worldBossFailCount + 1
            print(string.format("世界首领无次数，今日失败次数: %d", state.worldBossFailCount))
        elseif reason == "boss_dead" then
            print("世界首领Boss已死亡，稍后重试")
        elseif reason == "challenge_failed" then
            print("世界首领前往挑战失败，稍后重试")
        else
            -- 没有reason或其他情况说明是红点检测失败或其他失败，也增加失败计数
            state.worldBossFailCount = state.worldBossFailCount + 1
            print(string.format("世界首领失败（可能无红点），今日失败次数: %d", state.worldBossFailCount))
        end
    else
        -- 成功时重置失败计数器（说明有次数且能正常挑战）
        if state.worldBossFailCount > 0 then
            print("世界首领任务完成，重置失败计数器")
            state.worldBossFailCount = 0
        else
            print("世界首领任务完成")
        end
    end
    
    state.isExecutingTask = false
    state.currentTask = ""
    state.isInMap = false  -- 执行任务后需要重新进图
end

-- 执行每日任务
local function executeDailyTask(taskName)
    state.isExecutingTask = true
    state.currentTask = taskName
    updateHUD("正在执行" .. taskName .. "任务...")
    
    -- 使用pcall包装任务函数，确保异常不会导致脚本崩溃
    local success, result = pcall(function()
        if taskName == "领取宝箱和散人福利" then
            if config.userConfig.升级宝箱 and config.userConfig.升级宝箱.enable then
                character:领取宝箱()
            end
            if config.userConfig.散人福利 and config.userConfig.散人福利.enable then
                character:领取散人福利()
            end
        elseif taskName == "星空秘境" then
            if config.userConfig.星空秘境 and config.userConfig.星空秘境.enable then
                character:星空秘境()
            end
        elseif taskName == "武林争霸" then
            if config.userConfig.武林争霸 and config.userConfig.武林争霸.enable then
                character:武林争霸()
            end
        end
        return true
    end)
    
    if success then
        print(taskName .. "任务完成")
    else
        print(taskName .. "任务执行出错: " .. tostring(result))
    end
    
    state.isExecutingTask = false
    state.currentTask = ""
    state.isInMap = false  -- 执行任务后需要重新进图
end

-- 执行启动任务
local function executeStartupTasks()
    -- 检查是否是新的一天
    local currentDate = getCurrentDate()
    if taskRecord.lastStartupDate == currentDate then
        print("今日启动任务已执行，跳过")
        return
    end
    
    state.isExecutingTask = true
    
    -- 执行个人首领
    if config.userConfig.个人首领 and config.userConfig.个人首领.enable then
        state.currentTask = "个人首领"
        updateHUD("正在执行个人首领任务...")
        
        local success, result = pcall(function()
            return character:个人首领(config.userConfig.个人首领.分配次数)
        end)
        
        if success then
            print("个人首领任务完成")
        else
            print("个人首领任务执行出错: " .. tostring(result))
        end
    end
    
    -- 执行入道天途
    if config.userConfig.入道天途 and config.userConfig.入道天途.enable then
        state.currentTask = "入道天途"
        updateHUD("正在执行入道天途任务...")
        
        local success, result = pcall(function()
            return character:入道天途()
        end)
        
        if success then
            print("入道天途任务完成")
        else
            print("入道天途任务执行出错: " .. tostring(result))
        end
    end
    
    taskRecord.startupTasksDone = true
    taskRecord.lastStartupDate = currentDate
    
    state.isExecutingTask = false
    state.currentTask = ""
    state.isInMap = false  -- 执行任务后需要重新进图
end

-- 检查周期性任务
local function checkPeriodicTasks()
    -- 检查禁地任务（每6小时）
    if config.userConfig.禁地 and config.userConfig.禁地.enable then
        if not timers.forbiddenLand then
            -- 启动时立即执行一次禁地
            print("启动时执行禁地任务")
            executeJinDi()
            
            -- 初始化定时器，6小时后再次执行
            timers.forbiddenLand = utils.Timer:new(6, "h")
            print("初始化禁地定时器，间隔: 6小时")
        elseif timers.forbiddenLand:isFinished() then
            executeJinDi()
            timers.forbiddenLand:reset()
            
            -- 执行任务后重新进图
            if state.isHanging and not state.isInMap then
                enterMap()
            end
        end
    end
    
    -- 检查世界首领任务（每15分钟）
    if config.userConfig.世界Boss and config.userConfig.世界Boss.enable then
        if not timers.worldBoss then
            timers.worldBoss = utils.Timer:new(3, "min")
            print("初始化世界首领定时器，间隔: 15分钟")
        end
        
        if timers.worldBoss:isFinished() then
            executeWorldBoss()
            timers.worldBoss:reset()
            
            -- 执行任务后重新进图
            if state.isHanging and not state.isInMap then
                enterMap()
            end
        end
    end
end

-- 执行千里传音
local function executeQianLiChuanYin()
    if not config.userConfig.千里传音 or not config.userConfig.千里传音.enable then
        print("千里传音功能未启用，跳过执行")
        return
    end

    if not config.userConfig.千里传音.传音内容 or config.userConfig.千里传音.传音内容 == "" then
        print("千里传音内容为空，跳过执行")
        return
    end

    state.isExecutingTask = true
    state.currentTask = "千里传音"
    updateHUD("正在执行千里传音...")

    -- 使用pcall包装千里传音函数，确保异常不会导致脚本崩溃
    local success, result = pcall(function()
        return character:千里传音(config.userConfig.千里传音.传音内容)
    end)
    
    if not success then
        print("千里传音执行出错: " .. tostring(result))
    elseif result then
        print("千里传音执行完成")
    else
        print("千里传音执行失败")
    end

    state.isExecutingTask = false
    state.currentTask = ""
end

-- 检查每日定时任务
local function checkDailyTasks()
    local hour, minute = getCurrentHourMinute()
    local currentTime = hour * 60 + minute  -- 转换为分钟

    -- 定义每日任务
    local dailyTasks = {
        {time = 22 * 60 + 10, name = "领取宝箱和散人福利", executed = false},
        {time = 20 * 60 + 2, name = "星空秘境", executed = false},  -- 改为8:02
        {time = 3 * 60, name = "武林争霸", executed = false},
        {time = 15 * 60, name = "武林争霸", executed = false},
    }

    -- 定义千里传音任务
    local chuanYinTasks = {
        {time = 19 * 60 + 40, name = "千里传音", executed = false},  -- 19:40
        {time = 19 * 60 + 58, name = "千里传音", executed = false},  -- 19:58
        {time = 20 * 60 + 29, name = "千里传音", executed = false},  -- 20:29
    }

    -- 定义活跃地图进图任务
    local mapTasks = {
        {time = 0 * 60 + 2, name = "活跃地图进图", executed = false},  -- 00:02
    }
    
    -- 检查并执行到时间的任务
    for _, task in ipairs(dailyTasks) do
        local taskKey = task.name .. "_" .. task.time
        if not timers.dailyTasks[taskKey] then
            timers.dailyTasks[taskKey] = {executed = false, lastDate = ""}
        end

        local currentDate = getCurrentDate()
        local taskTimer = timers.dailyTasks[taskKey]

        -- 如果是新的一天，重置执行状态
        if taskTimer.lastDate ~= currentDate then
            taskTimer.executed = false
            taskTimer.lastDate = currentDate
        end

        -- 检查是否到执行时间
        if currentTime >= task.time and currentTime < task.time + 5 and not taskTimer.executed then
            executeDailyTask(task.name)
            taskTimer.executed = true

            -- 执行任务后重新进图
            if state.isHanging and not state.isInMap then
                enterMap()
            end
        end
    end

    -- 检查并执行千里传音任务
    for _, task in ipairs(chuanYinTasks) do
        local taskKey = task.name .. "_" .. task.time
        if not timers.dailyTasks[taskKey] then
            timers.dailyTasks[taskKey] = {executed = false, lastDate = ""}
        end

        local currentDate = getCurrentDate()
        local taskTimer = timers.dailyTasks[taskKey]

        -- 如果是新的一天，重置执行状态
        if taskTimer.lastDate ~= currentDate then
            taskTimer.executed = false
            taskTimer.lastDate = currentDate
        end

        -- 检查是否到执行时间
        if currentTime >= task.time and currentTime < task.time + 5 and not taskTimer.executed then
            executeQianLiChuanYin()
            taskTimer.executed = true
        end
    end

    -- 检查并执行活跃地图进图任务
    for _, task in ipairs(mapTasks) do
        local taskKey = task.name .. "_" .. task.time
        if not timers.dailyTasks[taskKey] then
            timers.dailyTasks[taskKey] = {executed = false, lastDate = ""}
        end

        local currentDate = getCurrentDate()
        local taskTimer = timers.dailyTasks[taskKey]

        -- 如果是新的一天，重置执行状态
        if taskTimer.lastDate ~= currentDate then
            taskTimer.executed = false
            taskTimer.lastDate = currentDate
        end

        -- 检查是否到执行时间且活跃地图功能开启
        if currentTime >= task.time and currentTime < task.time + 5 and not taskTimer.executed then
            if config.userConfig.活跃地图 and config.userConfig.活跃地图.enable then
                print("00:02定时进入活跃地图")
                updateHUD("00:02定时进入活跃地图")
                state.isInMap = false  -- 强制重新进图
                enterMap()
                taskTimer.executed = true
            else
                print("活跃地图功能未开启，跳过00:02进图任务")
                taskTimer.executed = true
            end
        end
    end
end

-- 主挂机循环
function coreLogic.startHanging()
    print("开始主线挂机逻辑")
    updateHUD("正在初始化挂机系统...")
    
    -- 初始化
    state.isHanging = true
    initShoutTimers()
    
    -- 执行启动任务
    if not taskRecord.startupTasksDone then
        executeStartupTasks()
    end
    
    -- 首次检查周期性任务（会立即执行禁地）
    checkPeriodicTasks()
    
    -- 进入地图
    if enterMap() then
        -- 开启自动战斗
        utils.sleepLog(2000, "等待进图完成")
        character:开关自动战斗功能()
        updateHUD("已开启自动战斗，开始挂机")
    end
    
    -- 主循环
    while state.isHanging do
        -- 如果不在执行任务且需要进图
        if not state.isExecutingTask and not state.isInMap then
            enterMap()
            if state.isInMap then
                utils.sleepLog(2000, "等待进图完成")
                character:开关自动战斗功能()
            end
        end
        
        -- 挂机时的定期操作（仅在纯挂机状态下执行）
        if state.isInMap and not state.isExecutingTask then
            -- 定期攻击（每20毫秒）
            if not timers.attackTimer then
                timers.attackTimer = utils.Timer:new(20, "ms")
            end

            local statusParts = {"挂机中"}

            if timers.attackTimer:isFinished() then
                executeAttack()
                timers.attackTimer:reset()
            end

            -- 检查并执行喊话，获取喊话状态
            local shoutStatus = checkAndExecuteShouts()
            if shoutStatus then
                table.insert(statusParts, shoutStatus)
            end

            -- 检查定时重启，获取重启状态
            local restartStatus = checkRestart()
            if restartStatus then
                table.insert(statusParts, restartStatus)
            end

            -- 前台应用检测（仅在纯挂机喊话阶段进行）
            -- 初始化前台检测定时器（每30秒检查一次，避免频繁检测）
            if not timers.frontAppCheckTimer then
                timers.frontAppCheckTimer = utils.Timer:new(1, "s")
            end
            
            if timers.frontAppCheckTimer:isFinished() then
                local frontApp = utils.getFrontAppNameFast()
                local expectedApp = config.devConfig.应用包名
                if frontApp and frontApp ~= expectedApp then
                    print(string.format("检测到前台应用不匹配: 当前=%s, 期望=%s, 执行重启", frontApp, expectedApp))
                    executeRestart()
                    return  -- 重启后退出当前循环
                else
                    print("前台应用检测正常: " .. tostring(frontApp))
                end
                timers.frontAppCheckTimer:reset()
            end

            -- 断开检测（每30秒检查一次，检测是否有"断开"文本）
            if not timers.disconnectCheckTimer then
                timers.disconnectCheckTimer = utils.Timer:new(30, "s")
            end
            
            if timers.disconnectCheckTimer:isFinished() then
                local disconnectFound = common.findTextAndGetCenter("断开", { 876, 187, 1098, 260 }, true)
                if disconnectFound then
                    print("检测到断开提示，执行重启")
                    executeRestart()
                    return  -- 重启后退出当前循环
                else
                    print("断开检测正常")
                end
                timers.disconnectCheckTimer:reset()
            end

            -- 组合显示状态
            updateHUD(table.concat(statusParts, " | "))
        end
        
        -- 检查定时任务（不受挂机状态限制）
        checkPeriodicTasks()
        checkDailyTasks()
        
        -- 主循环延时，避免过于频繁的状态更新（每秒更新一次状态即可）
        utils.sleepLog(10, "主循环延时")
    end
end

-- 停止挂机
function coreLogic.stopHanging()
    print("停止挂机逻辑")
    state.isHanging = false
    updateHUD("挂机已停止")
end

-- 获取当前状态
function coreLogic.getState()
    return state
end

-- 重置状态
function coreLogic.resetState()
    state.isInMap = false
    state.isExecutingTask = false
    state.currentTask = ""
    updateHUD("状态已重置")
end

return coreLogic